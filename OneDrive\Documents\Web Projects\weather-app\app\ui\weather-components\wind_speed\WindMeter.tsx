"use client";

import styles from "./WindMeter.module.css";
import { useWeather } from "../../../context/WeatherContext";
import { useEffect, useState } from "react";

export default function WindMeter() {
  const { weather } = useWeather();
  const [windSpeed, setWindSpeed] = useState(0);

  useEffect(() => {
    setWindSpeed(80);
  }, []);

  // Calculate needle rotation (0-50 mph range, -90 to 90 degrees)
  const maxSpeed = 50;
  const clampedSpeed = Math.min(windSpeed, maxSpeed);
  const needleRotation = (clampedSpeed / maxSpeed) * 180 - 90;

  // Generate speed marks (0, 10, 20, 30, 40, 50)
  const speedMarks = [0, 10, 20, 30, 40, 50, 6];

  return (
    <div className={styles.wind_meter}>
      <div className={styles.speedometer}>
        {/* Speed arc background */}
        <div className={styles.speed_arc}></div>

        {/* Speed marks */}
        {speedMarks.map((speed) => {
          const angle = (speed / maxSpeed) * 180 - 90;
          return (
            <div
              key={speed}
              className={styles.speed_mark}
              style={{
                transform: `rotate(${angle}deg)`,
              }}
            >
              <div className={styles.speed_mark_line}></div>
              <div className={styles.speed_mark_label}>{speed}</div>
            </div>
          );
        })}

        {/* Needle */}
        <div
          className={styles.needle}
          style={{
            transform: `rotate(${needleRotation}deg)`,
          }}
        ></div>

        {/* Center dot */}
        <div className={styles.center_dot}></div>

        {/* Speed display */}
        <div className={styles.speed_display}>
          <div className={styles.speed_unit}>MPH</div>
        </div>
      </div>
    </div>
  );
}
