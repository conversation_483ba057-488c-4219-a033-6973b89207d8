import styles from "./WindMeter.module.css";
import { useWeather } from "../../../context/WeatherContext";

export default function WindMeter() {
  const { weather } = useWeather();
  return (
    <div className={styles.wind_meter}>
      <div className={styles.wind_meter_bar}>a</div>
      <div className={styles.wind_meter_bar}></div>
      <div className={styles.wind_meter_bar}></div>
      <div className={styles.wind_meter_bar}></div>
      <div className={styles.wind_meter_bar}></div>
      <div className={styles.wind_meter_bar}></div>
      <div className={styles.wind_meter_bar}></div>
    </div>
  );
}
