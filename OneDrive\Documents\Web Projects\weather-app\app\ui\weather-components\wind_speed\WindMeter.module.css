div.wind_meter {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 20px;
}

div.speedometer {
  position: relative;
  width: 200px;
  height: 120px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

div.speed_arc {
  position: absolute;
  width: 180px;
  height: 90px;

  border-bottom: none;
  border-radius: 180px 180px 0 0;

  -webkit-mask: radial-gradient(
    circle at 50% 100%,
    transparent 70px,
    rgb(255, 255, 255) 72px
  );
  mask: radial-gradient(circle at 50% 100%, transparent 70px, black 72px);
}

div.speed_mark {
  position: absolute;
  width: 2px;
  height: 90px;
  transform-origin: 50% 100%;
  bottom: 0;
  left: 50%;
  margin-left: -1px;
}

div.speed_mark_line {
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 15px;
  background-color: var(--foreground);
}

div.speed_mark_label {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  font-weight: 600;
  color: var(--foreground);
}

div.needle {
  position: absolute;
  width: 3px;
  height: 70px;
  background-color: var(--foreground);
  transform-origin: 50% 100%;
  bottom: 0;
  left: 50%;
  margin-left: -1.5px;
  border-radius: 2px 2px 0 0;
  transition: transform 0.5s ease-in-out;
  z-index: 2;
}

div.center_dot {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: var(--foreground);
  border-radius: 50%;
  bottom: -6px;
  left: 50%;
  margin-left: -6px;
  z-index: 3;
}

div.speed_display {
  position: absolute;
  bottom: -40px;
  text-align: center;
}

div.speed_value {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  line-height: 1;
}

div.speed_unit {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}
