"use client";

import { useWeather } from "../../context/WeatherContext";
import React, { useEffect, useState } from "react";

const WindDirection = () => {
  const { weather } = useWeather();
  const [windDirection, setWindDirection] = useState<number>(0);

  useEffect(() => {
    setWindDirection(Math.round(weather?.windDirection ?? 0));
  }, [weather]);

  return <>{windDirection} &deg;</>;
};

export default WindDirection;
